/**
 * Tax Year Configuration Utilities
 * Provides dynamic tax year management instead of hard-coded values
 */

export interface TaxYear {
  year: number;
  label: string;
  isActive: boolean;
  filingDeadline: Date;
  extensionDeadline: Date;
  description?: string;
}

/**
 * Get the current tax year based on the current date
 * Tax year typically runs from January 1 to December 31
 * Filing is typically done in the following year
 */
export const getCurrentTaxYear = (): number => {
  const now = new Date();
  const currentYear = now.getFullYear();
  
  // If we're past April 15th, we're likely working on the previous year's taxes
  // Otherwise, we might still be working on the year before that
  const april15 = new Date(currentYear, 3, 15); // April 15th
  
  if (now > april15) {
    return currentYear - 1;
  } else {
    return currentYear - 1;
  }
};

/**
 * Get available tax years for filing
 * Typically allows filing for the current year and several previous years
 */
export const getAvailableTaxYears = (): TaxYear[] => {
  const currentTaxYear = getCurrentTaxYear();
  const years: TaxYear[] = [];
  
  // Allow filing for current year and 3 previous years
  for (let i = 0; i < 4; i++) {
    const year = currentTaxYear - i;
    const isCurrentYear = i === 0;
    
    years.push({
      year,
      label: `${year} Tax Year`,
      isActive: true,
      filingDeadline: new Date(year + 1, 3, 15), // April 15th of following year
      extensionDeadline: new Date(year + 1, 9, 15), // October 15th of following year
      description: isCurrentYear ? 'Current tax year' : `${i} year${i > 1 ? 's' : ''} ago`,
    });
  }
  
  return years;
};

/**
 * Get a specific tax year configuration
 */
export const getTaxYear = (year: number): TaxYear | null => {
  const availableYears = getAvailableTaxYears();
  return availableYears.find(ty => ty.year === year) || null;
};

/**
 * Check if a tax year is valid and available for filing
 */
export const isValidTaxYear = (year: number): boolean => {
  const availableYears = getAvailableTaxYears();
  return availableYears.some(ty => ty.year === year && ty.isActive);
};

/**
 * Get the default tax year (usually the current tax year)
 */
export const getDefaultTaxYear = (): number => {
  return getCurrentTaxYear();
};

/**
 * Format tax year for display
 */
export const formatTaxYear = (year: number): string => {
  return `${year} Tax Year`;
};

/**
 * Get tax year from URL parameter or default
 */
export const getTaxYearFromParams = (taxYearParam?: string): number => {
  if (taxYearParam) {
    const year = parseInt(taxYearParam, 10);
    if (!isNaN(year) && isValidTaxYear(year)) {
      return year;
    }
  }
  return getDefaultTaxYear();
};

/**
 * Check if filing deadline has passed for a tax year
 */
export const isFilingDeadlinePassed = (year: number): boolean => {
  const taxYear = getTaxYear(year);
  if (!taxYear) return true;
  
  return new Date() > taxYear.filingDeadline;
};

/**
 * Check if extension deadline has passed for a tax year
 */
export const isExtensionDeadlinePassed = (year: number): boolean => {
  const taxYear = getTaxYear(year);
  if (!taxYear) return true;
  
  return new Date() > taxYear.extensionDeadline;
};

/**
 * Get days remaining until filing deadline
 */
export const getDaysUntilFilingDeadline = (year: number): number => {
  const taxYear = getTaxYear(year);
  if (!taxYear) return 0;
  
  const now = new Date();
  const deadline = taxYear.filingDeadline;
  const diffTime = deadline.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return Math.max(0, diffDays);
};

/**
 * Get filing status message for a tax year
 */
export const getFilingStatusMessage = (year: number): string => {
  const daysRemaining = getDaysUntilFilingDeadline(year);
  
  if (daysRemaining === 0) {
    return 'Filing deadline is today!';
  } else if (daysRemaining < 0) {
    if (isExtensionDeadlinePassed(year)) {
      return 'Extension deadline has passed';
    } else {
      return 'Filing deadline passed - extension may be available';
    }
  } else if (daysRemaining <= 30) {
    return `${daysRemaining} days remaining to file`;
  } else {
    return `Filing deadline: ${getTaxYear(year)?.filingDeadline.toLocaleDateString()}`;
  }
};
