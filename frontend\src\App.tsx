import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { CssBaseline } from '@mui/material';
import { AuthProvider } from './context/AuthContext';
import { ThemeProvider } from './context/ThemeContext';

// Pages
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import Dashboard from './pages/Dashboard';
import PersonalInfo from './pages/PersonalInfo';
import Income from './pages/Income';
import IncomeSelection from './pages/IncomeSelection';
import Form1099INT from './pages/Form1099INT';
import Form1099DIV from './pages/Form1099DIV';
import ScheduleC from './pages/ScheduleC';
import Adjustments from './pages/Adjustments';
import Deductions from './pages/Deductions';
import Dependents from './pages/Dependents';
import Credits from './pages/Credits';
import Payments from './pages/Payments';
import Review from './pages/Review';

// Components
import ProtectedRoute from './components/ProtectedRoute';
import ErrorBoundary from './components/ErrorBoundary';

function App() {
  return (
    <ErrorBoundary>
      <ThemeProvider>
        <CssBaseline />
        <AuthProvider>
          <Router>
            <Routes>
              {/* Public routes */}
              <Route path="/" element={<Home />} />
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />

              {/* Protected routes */}
              <Route element={<ProtectedRoute />}>
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/tax-return/:taxYear/personal-info" element={<PersonalInfo />} />
                <Route path="/tax-return/:taxYear/income" element={<IncomeSelection />} />
                <Route path="/tax-return/:taxYear/income/w2" element={<Income />} />
                <Route path="/tax-return/:taxYear/income/interest" element={<Form1099INT />} />
                <Route path="/tax-return/:taxYear/income/dividends" element={<Form1099DIV />} />
                <Route path="/tax-return/:taxYear/income/self-employment" element={<ScheduleC />} />
                <Route path="/tax-return/:taxYear/adjustments" element={<Adjustments />} />
                <Route path="/tax-return/:taxYear/deductions" element={<Deductions />} />
                <Route path="/tax-return/:taxYear/dependents" element={<Dependents />} />
                <Route path="/tax-return/:taxYear/credits" element={<Credits />} />
                <Route path="/tax-return/:taxYear/payments" element={<Payments />} />
                <Route path="/tax-return/:taxYear/review" element={<Review />} />
              </Route>

              {/* Redirect to home for any unknown routes */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </Router>
        </AuthProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App
