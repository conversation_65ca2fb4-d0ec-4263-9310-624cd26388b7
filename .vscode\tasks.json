{"version": "2.0.0", "tasks": [{"label": "Start PostgreSQL for MCP", "type": "shell", "command": "docker-compose", "args": ["up", "-d", "postgres"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [], "detail": "Start PostgreSQL database for MCP server access"}, {"label": "Stop PostgreSQL", "type": "shell", "command": "docker-compose", "args": ["stop", "postgres"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [], "detail": "Stop PostgreSQL database"}, {"label": "Test MCP PostgreSQL Connection", "type": "shell", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://postgres:postgres@localhost:5432/bikhard_tax", "--test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [], "detail": "Test MCP PostgreSQL server connection"}, {"label": "Install MCP Dependencies", "type": "shell", "command": "npm", "args": ["install", "-g", "@modelcontextprotocol/server-filesystem", "@modelcontextprotocol/server-git", "@modelcontextprotocol/server-postgres", "@modelcontextprotocol/server-memory", "@modelcontextprotocol/server-brave-search", "@modelcontextprotocol/server-puppeteer"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [], "detail": "Install all MCP server dependencies globally"}, {"label": "Start Full Development Environment", "type": "shell", "command": "docker-compose", "args": ["-f", "docker-compose.dev.yml", "up", "-d"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [], "detail": "Start the full development environment with database"}, {"label": "Check MCP Server Status", "type": "shell", "command": "echo", "args": ["Checking MCP server configurations..."], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [], "detail": "Display current MCP server status and configuration"}]}