import React, { useState } from 'react';
import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Paper,
  Divider,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  ExpandMore,
  Info,
  Warning,
  CheckCircle,
  Error as ErrorIcon,
  Help,
  Calculate,
  Receipt,
  Business,
} from '@mui/icons-material';

interface FieldGuideProps {
  fieldName: string;
  value?: string | number;
  onCalculatorOpen?: () => void;
}

interface GuideContent {
  title: string;
  description: string;
  examples: string[];
  tips: string[];
  warnings?: string[];
  deductible: boolean;
  category: 'income' | 'expense' | 'business-info';
  irs_reference?: string;
  common_mistakes?: string[];
}

const fieldGuides: Record<string, GuideContent> = {
  businessName: {
    title: 'Business Name',
    description: 'The legal name of your business as it appears on your business license or registration.',
    examples: ['ABC Consulting LLC', '<PERSON>', 'Tech Solutions Inc.'],
    tips: [
      'Use the exact legal name of your business',
      'If you operate under a DBA (Doing Business As), use that name',
      'For sole proprietorships, you can use your own name'
    ],
    deductible: false,
    category: 'business-info'
  },
  grossReceipts: {
    title: 'Gross Receipts or Sales',
    description: 'Total income from your business before any deductions. Include all money received from customers.',
    examples: ['$50,000 from consulting services', '$25,000 from product sales', '$15,000 from freelance work'],
    tips: [
      'Include all income, even if not yet collected',
      'Include bartering income at fair market value',
      'Include income from credit card sales',
      'Do not subtract returns or allowances here'
    ],
    warnings: [
      'Underreporting income can result in penalties and interest',
      'The IRS may have records of payments made to you (1099s)'
    ],
    deductible: false,
    category: 'income',
    irs_reference: 'Publication 334, Chapter 7'
  },
  advertising: {
    title: 'Advertising',
    description: 'Costs for promoting your business to attract customers.',
    examples: ['$500 for Google Ads', '$200 for business cards', '$300 for website advertising'],
    tips: [
      'Include online advertising costs',
      'Business cards and promotional materials qualify',
      'Trade show booth costs are deductible',
      'Keep receipts for all advertising expenses'
    ],
    deductible: true,
    category: 'expense',
    common_mistakes: [
      'Forgetting to include online advertising',
      'Not tracking small promotional expenses'
    ]
  },
  carAndTruck: {
    title: 'Car and Truck Expenses',
    description: 'Vehicle expenses for business use. You can use actual expenses or standard mileage rate.',
    examples: ['$2,500 using standard mileage rate', '$3,000 in actual vehicle expenses'],
    tips: [
      'Keep a detailed mileage log',
      'Standard mileage rate for 2023: $0.655 per mile',
      'Actual expenses include gas, repairs, insurance, depreciation',
      'Only business portion is deductible'
    ],
    warnings: [
      'Cannot switch between methods for the same vehicle',
      'Must have records to support business use percentage'
    ],
    deductible: true,
    category: 'expense',
    irs_reference: 'Publication 463'
  },
  officeExpense: {
    title: 'Office Expense',
    description: 'Costs for office supplies and equipment used in your business.',
    examples: ['$150 for office supplies', '$500 for computer software', '$200 for printer ink'],
    tips: [
      'Include pens, paper, folders, and other supplies',
      'Software subscriptions for business use',
      'Small office equipment under $2,500',
      'Postage and shipping supplies'
    ],
    deductible: true,
    category: 'expense'
  },
  utilities: {
    title: 'Utilities',
    description: 'Business portion of utilities like electricity, gas, water, phone, and internet.',
    examples: ['$200 monthly for business phone', '$150 for business internet', '$100 for office electricity'],
    tips: [
      'Only deduct business portion if working from home',
      'Business phone lines are fully deductible',
      'Internet used for business is deductible',
      'Calculate home office percentage for mixed use'
    ],
    warnings: [
      'Personal use portion is not deductible',
      'Keep records showing business vs personal use'
    ],
    deductible: true,
    category: 'expense'
  },
  meals: {
    title: 'Meals',
    description: 'Business meals with clients, customers, or employees. Generally 50% deductible.',
    examples: ['$300 in client lunch meetings', '$150 for employee team meals'],
    tips: [
      'Must be ordinary and necessary for business',
      'Generally only 50% is deductible',
      'Keep receipts and note business purpose',
      'Record who attended and business discussed'
    ],
    warnings: [
      'Lavish or extravagant meals may not be deductible',
      'Personal meals are never deductible'
    ],
    deductible: true,
    category: 'expense',
    irs_reference: 'Publication 463, Chapter 2'
  },
  travel: {
    title: 'Travel',
    description: 'Business travel expenses including transportation, lodging, and incidental expenses.',
    examples: ['$800 for business conference travel', '$400 for client visit expenses'],
    tips: [
      'Must be away from home overnight',
      'Include airfare, hotels, car rentals',
      'Meals while traveling follow meal rules',
      'Keep detailed records of business purpose'
    ],
    warnings: [
      'Personal vacation expenses are not deductible',
      'Must have legitimate business purpose'
    ],
    deductible: true,
    category: 'expense'
  }
};

export const ScheduleCFieldGuide: React.FC<FieldGuideProps> = ({
  fieldName,
  value,
  onCalculatorOpen
}) => {
  const [detailsOpen, setDetailsOpen] = useState(false);
  const guide = fieldGuides[fieldName];

  if (!guide) {
    return null;
  }

  const getIcon = () => {
    switch (guide.category) {
      case 'income':
        return <Receipt color="success" />;
      case 'expense':
        return <Calculate color="primary" />;
      case 'business-info':
        return <Business color="info" />;
      default:
        return <Info />;
    }
  };

  const getCategoryColor = () => {
    switch (guide.category) {
      case 'income':
        return 'success';
      case 'expense':
        return 'primary';
      case 'business-info':
        return 'info';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Button
        size="small"
        startIcon={<Help />}
        onClick={() => setDetailsOpen(true)}
        sx={{ mb: 1 }}
      >
        Field Guide
      </Button>

      <Dialog
        open={detailsOpen}
        onClose={() => setDetailsOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {getIcon()}
            <Typography variant="h6">{guide.title}</Typography>
            <Chip
              label={guide.category.replace('-', ' ')}
              color={getCategoryColor() as any}
              size="small"
            />
            {guide.deductible && (
              <Chip label="Tax Deductible" color="success" size="small" />
            )}
          </Box>
        </DialogTitle>

        <DialogContent>
          <Typography variant="body1" paragraph>
            {guide.description}
          </Typography>

          {/* Examples */}
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography variant="subtitle1">Examples</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                {guide.examples.map((example, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <CheckCircle color="success" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={example} />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>

          {/* Tips */}
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography variant="subtitle1">Tips & Best Practices</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                {guide.tips.map((tip, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <Info color="info" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={tip} />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>

          {/* Warnings */}
          {guide.warnings && guide.warnings.length > 0 && (
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Typography variant="subtitle1" color="warning.main">
                  Important Warnings
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <List dense>
                  {guide.warnings.map((warning, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <Warning color="warning" fontSize="small" />
                      </ListItemIcon>
                      <ListItemText primary={warning} />
                    </ListItem>
                  ))}
                </List>
              </AccordionDetails>
            </Accordion>
          )}

          {/* Common Mistakes */}
          {guide.common_mistakes && guide.common_mistakes.length > 0 && (
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Typography variant="subtitle1" color="error.main">
                  Common Mistakes to Avoid
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <List dense>
                  {guide.common_mistakes.map((mistake, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <ErrorIcon color="error" fontSize="small" />
                      </ListItemIcon>
                      <ListItemText primary={mistake} />
                    </ListItem>
                  ))}
                </List>
              </AccordionDetails>
            </Accordion>
          )}

          {/* IRS Reference */}
          {guide.irs_reference && (
            <Paper sx={{ p: 2, mt: 2, bgcolor: 'grey.50' }}>
              <Typography variant="subtitle2" gutterBottom>
                IRS Reference
              </Typography>
              <Typography variant="body2">
                {guide.irs_reference}
              </Typography>
            </Paper>
          )}

          {/* Current Value */}
          {value !== undefined && (
            <Alert severity="info" sx={{ mt: 2 }}>
              <Typography variant="body2">
                Current value: {typeof value === 'number' ? `$${value.toLocaleString()}` : value}
              </Typography>
            </Alert>
          )}
        </DialogContent>

        <DialogActions>
          {onCalculatorOpen && guide.category === 'expense' && (
            <Button onClick={onCalculatorOpen} startIcon={<Calculate />}>
              Open Calculator
            </Button>
          )}
          <Button onClick={() => setDetailsOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ScheduleCFieldGuide;
