{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "jest --testPathIgnorePatterns=integration --testPathIgnorePatterns=e2e", "test:unit": "jest --testPathIgnorePatterns=integration --testPathIgnorePatterns=e2e", "test:integration": "jest --testPathPattern=integration", "test:e2e": "jest --testPathPattern=e2e", "test:all": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:docker": "docker-compose -f ../docker-compose.test.yml run --rm backend-test npm test", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "lint": "eslint ."}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "pg": "^8.14.1", "pg-hstore": "^2.3.4", "reflect-metadata": "^0.2.2", "sequelize": "^6.37.7", "sequelize-typescript": "^2.1.6", "tesseract.js": "^6.0.1", "uuid": "^9.0.1"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/node": "^22.14.0", "@types/supertest": "^6.0.3", "@types/uuid": "^9.0.8", "jest": "^29.7.0", "nodemon": "^3.1.9", "sqlite3": "^5.1.7", "supertest": "^6.3.4", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}