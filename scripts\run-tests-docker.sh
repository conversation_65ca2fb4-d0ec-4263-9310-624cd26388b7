#!/bin/bash

# BikHard USA Tax Filing Application - Docker Test Runner
# This script runs comprehensive tests in Docker environment

set -e

echo "🚀 Starting BikHard USA Tax Filing Application Test Suite"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    print_error "docker-compose is not installed. Please install docker-compose and try again."
    exit 1
fi

print_status "Cleaning up any existing test containers..."
docker-compose -f docker-compose.test.yml down --volumes --remove-orphans

print_status "Building test environment..."
docker-compose -f docker-compose.test.yml build

print_status "Starting PostgreSQL test database..."
docker-compose -f docker-compose.test.yml up -d postgres-test

# Wait for PostgreSQL to be ready
print_status "Waiting for PostgreSQL to be ready..."
sleep 10

# Check if PostgreSQL is ready
for i in {1..30}; do
    if docker-compose -f docker-compose.test.yml exec -T postgres-test pg_isready -U postgres; then
        print_success "PostgreSQL is ready!"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "PostgreSQL failed to start within 30 seconds"
        docker-compose -f docker-compose.test.yml logs postgres-test
        exit 1
    fi
    sleep 1
done

print_status "Running backend tests..."
if docker-compose -f docker-compose.test.yml run --rm backend-test npm test; then
    print_success "Backend tests passed!"
    BACKEND_TESTS_PASSED=true
else
    print_error "Backend tests failed!"
    BACKEND_TESTS_PASSED=false
fi

print_status "Running frontend tests..."
if docker-compose -f docker-compose.test.yml run --rm frontend-test npm test; then
    print_success "Frontend tests passed!"
    FRONTEND_TESTS_PASSED=true
else
    print_error "Frontend tests failed!"
    FRONTEND_TESTS_PASSED=false
fi

print_status "Running integration tests..."
if docker-compose -f docker-compose.test.yml run --rm backend-test npm run test:integration; then
    print_success "Integration tests passed!"
    INTEGRATION_TESTS_PASSED=true
else
    print_error "Integration tests failed!"
    INTEGRATION_TESTS_PASSED=false
fi

print_status "Running E2E tests..."
if docker-compose -f docker-compose.test.yml run --rm backend-test npm run test:e2e; then
    print_success "E2E tests passed!"
    E2E_TESTS_PASSED=true
else
    print_error "E2E tests failed!"
    E2E_TESTS_PASSED=false
fi

# Cleanup
print_status "Cleaning up test environment..."
docker-compose -f docker-compose.test.yml down --volumes

# Summary
echo ""
echo "=================================================="
echo "🏁 Test Results Summary"
echo "=================================================="

if [ "$BACKEND_TESTS_PASSED" = true ]; then
    print_success "✅ Backend Tests: PASSED"
else
    print_error "❌ Backend Tests: FAILED"
fi

if [ "$FRONTEND_TESTS_PASSED" = true ]; then
    print_success "✅ Frontend Tests: PASSED"
else
    print_error "❌ Frontend Tests: FAILED"
fi

if [ "$INTEGRATION_TESTS_PASSED" = true ]; then
    print_success "✅ Integration Tests: PASSED"
else
    print_error "❌ Integration Tests: FAILED"
fi

if [ "$E2E_TESTS_PASSED" = true ]; then
    print_success "✅ E2E Tests: PASSED"
else
    print_error "❌ E2E Tests: FAILED"
fi

# Check if all tests passed
if [ "$BACKEND_TESTS_PASSED" = true ] && [ "$FRONTEND_TESTS_PASSED" = true ] && [ "$INTEGRATION_TESTS_PASSED" = true ] && [ "$E2E_TESTS_PASSED" = true ]; then
    echo ""
    print_success "🎉 ALL TESTS PASSED! Application is ready for deployment."
    echo ""
    exit 0
else
    echo ""
    print_error "❌ Some tests failed. Please fix the issues before deployment."
    echo ""
    exit 1
fi
