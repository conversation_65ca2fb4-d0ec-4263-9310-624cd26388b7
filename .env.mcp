# MCP (Model Context Protocol) Configuration
# Copy this file to .env.local and update with your actual values

# Database Configuration for MCP PostgreSQL Server
MCP_DB_HOST=localhost
MCP_DB_PORT=5432
MCP_DB_NAME=bikhard_tax
MCP_DB_USER=postgres
MCP_DB_PASSWORD=postgres
MCP_DB_URL=postgresql://postgres:postgres@localhost:5432/bikhard_tax

# Brave Search API Configuration (Optional)
# Get your API key from: https://api.search.brave.com/
BRAVE_API_KEY=your-brave-api-key-here

# MCP Server Configuration
MCP_FILESYSTEM_ROOT=.
MCP_GIT_REPOSITORY=.
MCP_MEMORY_STORAGE_PATH=./.mcp-memory

# Development Environment
NODE_ENV=development
DEBUG=mcp:*

# Application Configuration (for debugging with MCP)
JWT_SECRET=development_secret_for_mcp
PORT=5000

# Test Database (for MCP testing)
TEST_DB_NAME=bikhard_tax_test
TEST_DB_URL=postgresql://postgres:postgres@localhost:5432/bikhard_tax_test
