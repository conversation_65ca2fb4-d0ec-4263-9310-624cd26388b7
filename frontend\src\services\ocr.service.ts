import { api } from './api';

export interface OCRConfidenceScore {
  field: string;
  value: string;
  confidence: number; // 0-1 scale
  boundingBox?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

export interface OCRValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

export interface OCRResult {
  documentType: 'W-2' | '1099-INT' | '1099-DIV';
  extractedData: Record<string, any>;
  confidenceScores: OCRConfidenceScore[];
  validation: OCRValidationResult;
  processingTime: number;
  imagePreview?: string; // Base64 encoded image
}

export interface OCRProcessingOptions {
  documentType?: 'W-2' | '1099-INT' | '1099-DIV' | 'auto';
  enhanceImage?: boolean;
  validateData?: boolean;
  returnPreview?: boolean;
  confidenceThreshold?: number; // Minimum confidence required (0-1)
}

class OCRService {
  /**
   * Process a document using AI-powered OCR
   */
  async processDocument(
    file: File,
    options: OCRProcessingOptions = {}
  ): Promise<OCRResult> {
    const formData = new FormData();
    formData.append('document', file);
    formData.append('options', JSON.stringify({
      documentType: options.documentType || 'auto',
      enhanceImage: options.enhanceImage ?? true,
      validateData: options.validateData ?? true,
      returnPreview: options.returnPreview ?? false,
      confidenceThreshold: options.confidenceThreshold ?? 0.7,
    }));

    const response = await api.post('/ocr/process', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 30000, // 30 second timeout for OCR processing
    });

    return response.data;
  }

  /**
   * Process W-2 form specifically
   */
  async processW2(file: File): Promise<OCRResult> {
    return this.processDocument(file, {
      documentType: 'W-2',
      enhanceImage: true,
      validateData: true,
      returnPreview: true,
    });
  }

  /**
   * Process 1099-INT form specifically
   */
  async process1099INT(file: File): Promise<OCRResult> {
    return this.processDocument(file, {
      documentType: '1099-INT',
      enhanceImage: true,
      validateData: true,
      returnPreview: true,
    });
  }

  /**
   * Process 1099-DIV form specifically
   */
  async process1099DIV(file: File): Promise<OCRResult> {
    return this.processDocument(file, {
      documentType: '1099-DIV',
      enhanceImage: true,
      validateData: true,
      returnPreview: true,
    });
  }

  /**
   * Validate extracted data against known patterns
   */
  async validateExtractedData(
    documentType: string,
    extractedData: Record<string, any>
  ): Promise<OCRValidationResult> {
    const response = await api.post('/ocr/validate', {
      documentType,
      extractedData,
    });

    return response.data;
  }

  /**
   * Get supported document types
   */
  async getSupportedDocumentTypes(): Promise<string[]> {
    const response = await api.get('/ocr/supported-types');
    return response.data.types;
  }

  /**
   * Get OCR processing status for a job
   */
  async getProcessingStatus(jobId: string): Promise<{
    status: 'pending' | 'processing' | 'completed' | 'failed';
    progress: number;
    result?: OCRResult;
    error?: string;
  }> {
    const response = await api.get(`/ocr/status/${jobId}`);
    return response.data;
  }

  /**
   * Enhance image quality before OCR processing
   */
  async enhanceImage(file: File): Promise<Blob> {
    const formData = new FormData();
    formData.append('image', file);

    const response = await api.post('/ocr/enhance-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      responseType: 'blob',
    });

    return response.data;
  }

  /**
   * Get confidence threshold recommendations for different document types
   */
  getConfidenceThresholds(): Record<string, number> {
    return {
      'W-2': 0.8,
      '1099-INT': 0.75,
      '1099-DIV': 0.75,
      'auto': 0.7,
    };
  }

  /**
   * Format extracted data for form submission
   */
  formatExtractedDataForForm(
    documentType: string,
    extractedData: Record<string, any>
  ): Record<string, any> {
    switch (documentType) {
      case 'W-2':
        return this.formatW2Data(extractedData);
      case '1099-INT':
        return this.format1099INTData(extractedData);
      case '1099-DIV':
        return this.format1099DIVData(extractedData);
      default:
        return extractedData;
    }
  }

  private formatW2Data(data: Record<string, any>): Record<string, any> {
    return {
      employerName: data.employerName || '',
      employerEIN: data.employerEIN || '',
      employeeSSN: data.employeeSSN || '',
      wages: parseFloat(data.wages) || 0,
      federalIncomeTaxWithheld: parseFloat(data.federalIncomeTaxWithheld) || 0,
      socialSecurityWages: parseFloat(data.socialSecurityWages) || 0,
      socialSecurityTaxWithheld: parseFloat(data.socialSecurityTaxWithheld) || 0,
      medicareWages: parseFloat(data.medicareWages) || 0,
      medicareTaxWithheld: parseFloat(data.medicareTaxWithheld) || 0,
      stateWages: parseFloat(data.stateWages) || 0,
      stateIncomeTaxWithheld: parseFloat(data.stateIncomeTaxWithheld) || 0,
    };
  }

  private format1099INTData(data: Record<string, any>): Record<string, any> {
    return {
      payerName: data.payerName || '',
      payerTIN: data.payerTIN || '',
      recipientTIN: data.recipientTIN || '',
      interestIncome: parseFloat(data.interestIncome) || 0,
      earlyWithdrawalPenalty: parseFloat(data.earlyWithdrawalPenalty) || 0,
      federalIncomeTaxWithheld: parseFloat(data.federalIncomeTaxWithheld) || 0,
      investmentExpenses: parseFloat(data.investmentExpenses) || 0,
      foreignTaxPaid: parseFloat(data.foreignTaxPaid) || 0,
    };
  }

  private format1099DIVData(data: Record<string, any>): Record<string, any> {
    return {
      payerName: data.payerName || '',
      payerTIN: data.payerTIN || '',
      recipientTIN: data.recipientTIN || '',
      ordinaryDividends: parseFloat(data.ordinaryDividends) || 0,
      qualifiedDividends: parseFloat(data.qualifiedDividends) || 0,
      totalCapitalGainDistributions: parseFloat(data.totalCapitalGainDistributions) || 0,
      federalIncomeTaxWithheld: parseFloat(data.federalIncomeTaxWithheld) || 0,
      investmentExpenses: parseFloat(data.investmentExpenses) || 0,
      foreignTaxPaid: parseFloat(data.foreignTaxPaid) || 0,
    };
  }
}

export const ocrService = new OCRService();
