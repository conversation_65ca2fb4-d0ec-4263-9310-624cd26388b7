import React from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Chip,
  Alert,
} from '@mui/material';
import { SelectChangeEvent } from '@mui/material/Select';
import {
  getAvailableTaxYears,
  formatTaxYear,
  getDaysUntilFilingDeadline,
  getFilingStatusMessage,
  isFilingDeadlinePassed,
} from '../../utils/taxYears';

interface TaxYearSelectorProps {
  selectedYear: number;
  onYearChange: (year: number) => void;
  label?: string;
  showFilingStatus?: boolean;
  variant?: 'outlined' | 'filled' | 'standard';
  size?: 'small' | 'medium';
  fullWidth?: boolean;
  disabled?: boolean;
}

export const TaxYearSelector: React.FC<TaxYearSelectorProps> = ({
  selectedYear,
  onYearChange,
  label = 'Tax Year',
  showFilingStatus = true,
  variant = 'outlined',
  size = 'medium',
  fullWidth = true,
  disabled = false,
}) => {
  const availableYears = getAvailableTaxYears();

  const handleChange = (event: SelectChangeEvent<number>) => {
    const year = event.target.value as number;
    onYearChange(year);
  };

  const getStatusChipColor = (year: number) => {
    const daysRemaining = getDaysUntilFilingDeadline(year);
    if (daysRemaining <= 0) return 'error';
    if (daysRemaining <= 30) return 'warning';
    return 'success';
  };

  const getStatusChipLabel = (year: number) => {
    const daysRemaining = getDaysUntilFilingDeadline(year);
    if (daysRemaining <= 0) return 'Deadline Passed';
    if (daysRemaining <= 30) return `${daysRemaining} days left`;
    return 'On Time';
  };

  return (
    <Box>
      <FormControl 
        variant={variant} 
        size={size} 
        fullWidth={fullWidth}
        disabled={disabled}
      >
        <InputLabel id="tax-year-select-label">{label}</InputLabel>
        <Select
          labelId="tax-year-select-label"
          id="tax-year-select"
          value={selectedYear}
          label={label}
          onChange={handleChange}
        >
          {availableYears.map((taxYear) => (
            <MenuItem key={taxYear.year} value={taxYear.year}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                <Typography>{formatTaxYear(taxYear.year)}</Typography>
                {showFilingStatus && (
                  <Chip
                    label={getStatusChipLabel(taxYear.year)}
                    color={getStatusChipColor(taxYear.year)}
                    size="small"
                    variant="outlined"
                  />
                )}
              </Box>
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {showFilingStatus && (
        <Box sx={{ mt: 1 }}>
          {isFilingDeadlinePassed(selectedYear) ? (
            <Alert severity="warning" size="small">
              {getFilingStatusMessage(selectedYear)}
            </Alert>
          ) : (
            <Typography variant="caption" color="text.secondary">
              {getFilingStatusMessage(selectedYear)}
            </Typography>
          )}
        </Box>
      )}
    </Box>
  );
};

export default TaxYearSelector;
