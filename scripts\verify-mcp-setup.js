#!/usr/bin/env node

/**
 * MCP Setup Verification Script
 * This script verifies that MCP servers can be properly initialized
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying MCP Setup...\n');

// Check if VS Code settings exist
const vscodeSettingsPath = path.join(__dirname, '..', '.vscode', 'settings.json');
if (fs.existsSync(vscodeSettingsPath)) {
  console.log('✅ VS Code settings.json found');
  
  try {
    const settings = JSON.parse(fs.readFileSync(vscodeSettingsPath, 'utf8'));
    if (settings['github.copilot.chat.experimental.mcpServers']) {
      console.log('✅ MCP servers configuration found');
      const servers = Object.keys(settings['github.copilot.chat.experimental.mcpServers']);
      console.log(`   Configured servers: ${servers.join(', ')}`);
    } else {
      console.log('❌ MCP servers configuration not found');
    }
  } catch (error) {
    console.log('❌ Error reading VS Code settings:', error.message);
  }
} else {
  console.log('❌ VS Code settings.json not found');
}

// Check Node.js version
console.log('\n📦 Checking Node.js version...');
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
if (majorVersion >= 18) {
  console.log(`✅ Node.js ${nodeVersion} (compatible)`);
} else {
  console.log(`❌ Node.js ${nodeVersion} (requires v18 or higher)`);
}

// Check if npx is available
console.log('\n🔧 Checking npx availability...');
const npxCheck = spawn('npx', ['--version'], { stdio: 'pipe' });
npxCheck.on('close', (code) => {
  if (code === 0) {
    console.log('✅ npx is available');
  } else {
    console.log('❌ npx is not available');
  }
});

// Test MCP server availability
console.log('\n🌐 Testing MCP server availability...');

const testServers = [
  '@modelcontextprotocol/server-filesystem',
  '@modelcontextprotocol/server-git',
  '@modelcontextprotocol/server-postgres',
  '@modelcontextprotocol/server-memory'
];

let serverTestsCompleted = 0;
const totalServers = testServers.length;

testServers.forEach((server) => {
  const testProcess = spawn('npx', ['-y', server, '--help'], { 
    stdio: 'pipe',
    timeout: 10000
  });
  
  testProcess.on('close', (code) => {
    serverTestsCompleted++;
    if (code === 0) {
      console.log(`✅ ${server} is accessible`);
    } else {
      console.log(`⚠️  ${server} may need installation`);
    }
    
    if (serverTestsCompleted === totalServers) {
      console.log('\n🎯 MCP Setup Verification Complete!');
      console.log('\nNext steps:');
      console.log('1. Restart VS Code to load MCP configuration');
      console.log('2. Start your PostgreSQL database: docker-compose up postgres');
      console.log('3. Test MCP in GitHub Copilot Chat with: "List files in current directory"');
      console.log('4. For database access: "Show me the database schema"');
    }
  });
  
  testProcess.on('error', (error) => {
    serverTestsCompleted++;
    console.log(`❌ Error testing ${server}:`, error.message);
    
    if (serverTestsCompleted === totalServers) {
      console.log('\n🎯 MCP Setup Verification Complete!');
    }
  });
});

// Check database connectivity (if running)
console.log('\n🗄️  Checking database connectivity...');
const { Client } = require('pg').Client || null;

if (Client) {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'bikhard_tax',
    user: 'postgres',
    password: 'postgres',
  });

  client.connect()
    .then(() => {
      console.log('✅ PostgreSQL database is accessible');
      return client.end();
    })
    .catch((error) => {
      console.log('⚠️  PostgreSQL database not accessible:', error.message);
      console.log('   Start with: docker-compose up postgres');
    });
} else {
  console.log('⚠️  pg module not found, skipping database test');
}

console.log('\n📚 For detailed setup instructions, see: MCP_SETUP_GUIDE.md');
