import express, { Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import { 
  processDocument, 
  validateExtractedData, 
  getSupportedDocumentTypes, 
  getProcessingStatus, 
  enhanceImage 
} from '../controllers/ocr.controller';
import { authenticateJWT } from '../middleware/auth.middleware';

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/temp/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const fileFilter = (req: any, file: any, cb: any) => {
  // Accept images and PDFs
  if (file.mimetype.startsWith('image/') || file.mimetype === 'application/pdf') {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only images and PDFs are allowed.'), false);
  }
};

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: fileFilter
});

// All routes require authentication
router.use('/', authenticateJWT);

// Process a document with OCR
router.post('/process', (req: Request, res: Response, next: Function) => {
  upload.single('document')(req, res, async (err: any) => {
    if (err) {
      // Handle multer errors
      if (err.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({ message: 'File size too large. Maximum size is 10MB.' });
      }
      if (err.message && err.message.includes('Invalid file type')) {
        return res.status(400).json({ message: 'Invalid file type. Only images and PDFs are allowed.' });
      }
      return res.status(400).json({ message: err.message || 'File upload error' });
    }

    try {
      await processDocument(req, res);
    } catch (error) {
      next(error);
    }
  });
});

// Validate extracted OCR data
router.post('/validate', async (req: Request, res: Response) => {
  try {
    await validateExtractedData(req, res);
  } catch (error) {
    console.error('OCR validation error:', error);
    res.status(500).json({ message: 'Server error during validation' });
  }
});

// Get supported document types
router.get('/supported-types', async (req: Request, res: Response) => {
  try {
    await getSupportedDocumentTypes(req, res);
  } catch (error) {
    console.error('Get supported types error:', error);
    res.status(500).json({ message: 'Server error getting supported types' });
  }
});

// Get OCR processing status for a job
router.get('/status/:jobId', async (req: Request, res: Response) => {
  try {
    await getProcessingStatus(req, res);
  } catch (error) {
    console.error('Get processing status error:', error);
    res.status(500).json({ message: 'Server error getting processing status' });
  }
});

// Enhance image quality before OCR processing
router.post('/enhance-image', (req: Request, res: Response, next: Function) => {
  upload.single('image')(req, res, async (err: any) => {
    if (err) {
      // Handle multer errors
      if (err.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({ message: 'File size too large. Maximum size is 10MB.' });
      }
      if (err.message && err.message.includes('Invalid file type')) {
        return res.status(400).json({ message: 'Invalid file type. Only images are allowed.' });
      }
      return res.status(400).json({ message: err.message || 'File upload error' });
    }

    try {
      await enhanceImage(req, res);
    } catch (error) {
      next(error);
    }
  });
});

export default router;
