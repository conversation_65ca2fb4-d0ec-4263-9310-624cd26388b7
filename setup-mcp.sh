#!/bin/bash

echo "Setting up MCP (Model Context Protocol) for VS Code..."
echo

echo "1. Checking Node.js installation..."
if command -v node &> /dev/null; then
    echo "✓ Node.js $(node --version) found"
else
    echo "✗ Node.js is not installed"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

echo
echo "2. Checking npx availability..."
if command -v npx &> /dev/null; then
    echo "✓ npx $(npx --version) found"
else
    echo "✗ npx is not available"
    exit 1
fi

echo
echo "3. Starting PostgreSQL database..."
if command -v docker-compose &> /dev/null; then
    docker-compose up -d postgres
    if [ $? -eq 0 ]; then
        echo "✓ PostgreSQL started successfully"
    else
        echo "⚠ Could not start PostgreSQL with Docker"
        echo "Make sure Dock<PERSON> is running and try: docker-compose up postgres"
    fi
else
    echo "⚠ Docker Compose not found, skipping database startup"
fi

echo
echo "4. Testing MCP server accessibility..."

echo "Testing filesystem server..."
if npx -y @modelcontextprotocol/server-filesystem --help &> /dev/null; then
    echo "✓ Filesystem server accessible"
else
    echo "✗ Filesystem server needs installation"
fi

echo "Testing git server..."
if npx -y @modelcontextprotocol/server-git --help &> /dev/null; then
    echo "✓ Git server accessible"
else
    echo "✗ Git server needs installation"
fi

echo "Testing postgres server..."
if npx -y @modelcontextprotocol/server-postgres --help &> /dev/null; then
    echo "✓ PostgreSQL server accessible"
else
    echo "✗ PostgreSQL server needs installation"
fi

echo
echo "5. Running verification script..."
if [ -f "scripts/verify-mcp-setup.js" ]; then
    node scripts/verify-mcp-setup.js
else
    echo "⚠ Verification script not found"
fi

echo
echo "MCP Setup Complete!"
echo
echo "Next steps:"
echo "1. Restart VS Code to load the new configuration"
echo "2. Open GitHub Copilot Chat"
echo "3. Test with: 'List files in current directory'"
echo "4. Test database: 'Show me the database schema'"
echo
echo "For detailed instructions, see: MCP_SETUP_GUIDE.md"
echo
