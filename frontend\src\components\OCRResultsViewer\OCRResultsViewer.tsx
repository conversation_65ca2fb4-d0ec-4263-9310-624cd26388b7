import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  ExpandMore,
  CheckCircle,
  Warning,
  Error as ErrorIcon,
  Info,
  Visibility,
  Edit,
  AutoFixHigh,
} from '@mui/icons-material';
import { OCRResult, OCRConfidenceScore, OCRValidationResult } from '../../services/ocr.service';

interface OCRResultsViewerProps {
  result: OCRResult;
  onDataEdit?: (field: string, value: string) => void;
  onAcceptData?: (data: Record<string, any>) => void;
  showImagePreview?: boolean;
}

export const OCRResultsViewer: React.FC<OCRResultsViewerProps> = ({
  result,
  onDataEdit,
  onAcceptData,
  showImagePreview = true,
}) => {
  const [imagePreviewOpen, setImagePreviewOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editingField, setEditingField] = useState<string>('');
  const [editingValue, setEditingValue] = useState<string>('');

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.9) return 'success';
    if (confidence >= 0.7) return 'warning';
    return 'error';
  };

  const getConfidenceLabel = (confidence: number) => {
    if (confidence >= 0.9) return 'High';
    if (confidence >= 0.7) return 'Medium';
    return 'Low';
  };

  const getValidationIcon = (type: 'error' | 'warning' | 'suggestion') => {
    switch (type) {
      case 'error':
        return <ErrorIcon color="error" />;
      case 'warning':
        return <Warning color="warning" />;
      case 'suggestion':
        return <Info color="info" />;
    }
  };

  const handleEditField = (field: string, currentValue: string) => {
    setEditingField(field);
    setEditingValue(currentValue);
    setEditDialogOpen(true);
  };

  const handleSaveEdit = () => {
    if (onDataEdit) {
      onDataEdit(editingField, editingValue);
    }
    setEditDialogOpen(false);
  };

  const overallConfidence = result.confidenceScores.reduce(
    (sum, score) => sum + score.confidence,
    0
  ) / result.confidenceScores.length;

  return (
    <Box>
      {/* Header */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                OCR Processing Results
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Document Type: {result.documentType}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Processing Time: {result.processingTime}ms
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ textAlign: { xs: 'left', md: 'right' } }}>
                <Chip
                  label={`${Math.round(overallConfidence * 100)}% Overall Confidence`}
                  color={getConfidenceColor(overallConfidence)}
                  size="large"
                  sx={{ mb: 1 }}
                />
                <Box>
                  <LinearProgress
                    variant="determinate"
                    value={overallConfidence * 100}
                    color={getConfidenceColor(overallConfidence)}
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Validation Results */}
      {(result.validation.errors.length > 0 ||
        result.validation.warnings.length > 0 ||
        result.validation.suggestions.length > 0) && (
        <Accordion sx={{ mb: 3 }}>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="h6">Validation Results</Typography>
            <Box sx={{ ml: 2 }}>
              {result.validation.errors.length > 0 && (
                <Chip label={`${result.validation.errors.length} Errors`} color="error" size="small" sx={{ mr: 1 }} />
              )}
              {result.validation.warnings.length > 0 && (
                <Chip label={`${result.validation.warnings.length} Warnings`} color="warning" size="small" sx={{ mr: 1 }} />
              )}
              {result.validation.suggestions.length > 0 && (
                <Chip label={`${result.validation.suggestions.length} Suggestions`} color="info" size="small" />
              )}
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={2}>
              {result.validation.errors.length > 0 && (
                <Grid item xs={12} md={4}>
                  <Typography variant="subtitle2" color="error" gutterBottom>
                    Errors
                  </Typography>
                  <List dense>
                    {result.validation.errors.map((error, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          {getValidationIcon('error')}
                        </ListItemIcon>
                        <ListItemText primary={error} />
                      </ListItem>
                    ))}
                  </List>
                </Grid>
              )}
              {result.validation.warnings.length > 0 && (
                <Grid item xs={12} md={4}>
                  <Typography variant="subtitle2" color="warning.main" gutterBottom>
                    Warnings
                  </Typography>
                  <List dense>
                    {result.validation.warnings.map((warning, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          {getValidationIcon('warning')}
                        </ListItemIcon>
                        <ListItemText primary={warning} />
                      </ListItem>
                    ))}
                  </List>
                </Grid>
              )}
              {result.validation.suggestions.length > 0 && (
                <Grid item xs={12} md={4}>
                  <Typography variant="subtitle2" color="info.main" gutterBottom>
                    Suggestions
                  </Typography>
                  <List dense>
                    {result.validation.suggestions.map((suggestion, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          {getValidationIcon('suggestion')}
                        </ListItemIcon>
                        <ListItemText primary={suggestion} />
                      </ListItem>
                    ))}
                  </List>
                </Grid>
              )}
            </Grid>
          </AccordionDetails>
        </Accordion>
      )}

      {/* Extracted Data */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">Extracted Data</Typography>
            <Box>
              {showImagePreview && result.imagePreview && (
                <Button
                  startIcon={<Visibility />}
                  onClick={() => setImagePreviewOpen(true)}
                  sx={{ mr: 1 }}
                >
                  View Image
                </Button>
              )}
              {onAcceptData && (
                <Button
                  variant="contained"
                  startIcon={<CheckCircle />}
                  onClick={() => onAcceptData(result.extractedData)}
                >
                  Accept Data
                </Button>
              )}
            </Box>
          </Box>

          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Field</TableCell>
                  <TableCell>Value</TableCell>
                  <TableCell>Confidence</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {result.confidenceScores.map((score, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {score.field}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {score.value || '-'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={`${Math.round(score.confidence * 100)}% (${getConfidenceLabel(score.confidence)})`}
                        color={getConfidenceColor(score.confidence)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {onDataEdit && (
                        <Button
                          size="small"
                          startIcon={<Edit />}
                          onClick={() => handleEditField(score.field, score.value)}
                        >
                          Edit
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Image Preview Dialog */}
      {result.imagePreview && (
        <Dialog
          open={imagePreviewOpen}
          onClose={() => setImagePreviewOpen(false)}
          maxWidth="lg"
          fullWidth
        >
          <DialogTitle>Document Preview</DialogTitle>
          <DialogContent>
            <Box sx={{ textAlign: 'center' }}>
              <img
                src={`data:image/jpeg;base64,${result.imagePreview}`}
                alt="Document preview"
                style={{ maxWidth: '100%', height: 'auto' }}
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setImagePreviewOpen(false)}>Close</Button>
          </DialogActions>
        </Dialog>
      )}

      {/* Edit Field Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)}>
        <DialogTitle>Edit Field: {editingField}</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <input
              type="text"
              value={editingValue}
              onChange={(e) => setEditingValue(e.target.value)}
              style={{ width: '100%', padding: '8px', fontSize: '16px' }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSaveEdit} variant="contained">
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default OCRResultsViewer;
