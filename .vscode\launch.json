{"version": "0.2.0", "configurations": [{"name": "Debug Backend with MCP", "type": "node", "request": "launch", "program": "${workspaceFolder}/backend/src/index.ts", "outFiles": ["${workspaceFolder}/backend/dist/**/*.js"], "env": {"NODE_ENV": "development", "DB_HOST": "localhost", "DB_PORT": "5432", "DB_NAME": "bikhard_tax", "DB_USER": "postgres", "DB_PASSWORD": "postgres", "JWT_SECRET": "development_secret", "PORT": "5000"}, "runtimeArgs": ["-r", "ts-node/register"], "skipFiles": ["<node_internals>/**"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "preLaunchTask": "Start PostgreSQL for MCP"}, {"name": "Debug Frontend with MCP", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/frontend", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "Debug Tests with Database", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--runInBand", "--<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "cwd": "${workspaceFolder}", "env": {"NODE_ENV": "test", "DB_HOST": "localhost", "DB_PORT": "5432", "DB_NAME": "bikhard_tax_test", "DB_USER": "postgres", "DB_PASSWORD": "postgres"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "preLaunchTask": "Start PostgreSQL for MCP"}, {"name": "Attach to Backend Container", "type": "node", "request": "attach", "port": 9229, "address": "localhost", "localRoot": "${workspaceFolder}/backend", "remoteRoot": "/app", "skipFiles": ["<node_internals>/**"]}], "compounds": [{"name": "Debug <PERSON> Stack with MCP", "configurations": ["Debug Backend with MCP", "Debug Frontend with MCP"], "stopAll": true}]}