version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: bikhard-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: bikhard_tax
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD:-postgres}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - bikhard-network

  # Backend API - Development with hot-reloading
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: bikhard-backend-dev
    restart: unless-stopped
    depends_on:
      - postgres
    environment:
      NODE_ENV: development
      PORT: 5000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: bikhard_tax
      DB_USER: postgres
      DB_PASSWORD: ${DB_PASSWORD:-postgres}
      JWT_SECRET: ${JWT_SECRET:-your_jwt_secret_key_here}
      JWT_EXPIRES_IN: 1d
    ports:
      - "5000:5000"
    volumes:
      - ./backend/src:/app/src
      - ./backend/uploads:/app/uploads
    networks:
      - bikhard-network

  # Frontend - Development with hot-reloading
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: bikhard-frontend-dev
    restart: unless-stopped
    depends_on:
      - backend
    environment:
      - VITE_API_URL=http://localhost:5000/api
    ports:
      - "3000:5173"
    volumes:
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
    networks:
      - bikhard-network

networks:
  bikhard-network:
    driver: bridge

volumes:
  postgres_data:
