import dotenv from 'dotenv';
import { Sequelize } from 'sequelize-typescript';
import models from '../models';

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Define global types
declare global {
  var testDb: Sequelize;
}

// Global test setup
beforeAll(async () => {
  console.log('Setting up test environment...');

  try {
    // Use PostgreSQL for testing as per requirements (not SQLite)
    // This ensures consistency between test and production environments
    const sequelize = new Sequelize({
      database: process.env.DB_NAME || 'bikhard_tax_test',
      username: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'postgres',
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      dialect: 'postgres',
      logging: false,
      models: models,
      pool: {
        max: 5,
        min: 0,
        acquire: 30000,
        idle: 10000
      },
      // For test environment, we want to recreate the database on each run
      define: {
        timestamps: true
      }
    });

    // Test the connection
    await sequelize.authenticate();
    console.log('PostgreSQL test database connection established successfully');

    // Sync models with database (force: true will drop tables and recreate them)
    await sequelize.sync({ force: true });
    console.log('Test database initialized with PostgreSQL (tables created)');

    // Store the database connection globally
    global.testDb = sequelize;
  } catch (error) {
    console.error('Failed to initialize test database:', error);
    console.error('Make sure PostgreSQL is running and accessible');
    throw error;
  }
});

// Global test teardown
afterAll(async () => {
  console.log('Cleaning up test environment...');

  // Close the test database connection
  if (global.testDb) {
    await global.testDb.close();
  }
});

// Reset database between tests with proper foreign key handling
afterEach(async () => {
  console.log('Test completed, resetting state...');

  try {
    // For PostgreSQL, we need to handle foreign key constraints properly
    if (global.testDb) {
      // Delete records in correct order to respect foreign key constraints
      // Delete child records first, then parent records
      const models = global.testDb.models;

      // Order matters: delete child tables first
      const deletionOrder = [
        'TaxCalculation',
        'EstimatedTaxPayment',
        'EarnedIncomeTaxCredit',
        'EducationCredit',
        'ChildDependentCareCredit',
        'ChildTaxCredit',
        'Dependent',
        'ScheduleA',
        'Adjustments',
        'ScheduleSE',
        'ScheduleC',
        'Form1099DIV',
        'Form1099INT',
        'W2StateInfo',
        'W2',
        'UploadedDocument',
        'Taxpayer', // Delete taxpayers before users
        'User'      // Delete users last
      ];

      // Delete records in the specified order
      for (const modelName of deletionOrder) {
        if (models[modelName]) {
          await models[modelName].destroy({
            where: {},
            truncate: true,
            cascade: true,
            force: true
          });
        }
      }

      // Re-enable foreign key checks
      await global.testDb.query('PRAGMA foreign_keys = ON;');

      console.log('Database state reset');
    }
  } catch (error) {
    console.error('Error resetting test database:', error);
    // Re-enable foreign key checks even if cleanup failed
    try {
      if (global.testDb) {
        await global.testDb.query('PRAGMA foreign_keys = ON;');
      }
    } catch (fkError) {
      console.error('Error re-enabling foreign key checks:', fkError);
    }
  }

  // Clear all mocks
  jest.clearAllMocks();
});
