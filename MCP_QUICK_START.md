# MCP Quick Start Guide

## What Was Set Up

I've configured MCP (Model Context Protocol) servers in your VS Code workspace to enhance GitHub Copilot Chat capabilities. Here's what was created:

### Configuration Files
- **`.vscode/settings.json`** - Main MCP configuration with 6 servers
- **`.vscode/tasks.json`** - VS Code tasks for MCP operations
- **`.vscode/launch.json`** - Debug configurations with MCP support
- **`.vscode/extensions.json`** - Recommended VS Code extensions

### Documentation
- **`MCP_SETUP_GUIDE.md`** - Comprehensive setup and usage guide
- **`MCP_QUICK_START.md`** - This quick start guide

### Setup Scripts
- **`setup-mcp.bat`** - Windows setup script
- **`setup-mcp.sh`** - Unix/Linux setup script
- **`scripts/verify-mcp-setup.js`** - Verification script

### Environment Configuration
- **`.env.mcp`** - Environment variables template for MCP

## Configured MCP Servers

1. **Filesystem Server** - File operations and navigation
2. **Git Server** - Repository operations and history
3. **PostgreSQL Server** - Database queries and schema inspection
4. **Memory Server** - Persistent conversation memory
5. **Brave Search Server** - Web search capabilities (optional)
6. **Puppeteer Server** - Web automation (optional)

## Quick Setup (3 Steps)

### Step 1: Run Setup Script
**Windows:**
```cmd
setup-mcp.bat
```

**Unix/Linux/Mac:**
```bash
chmod +x setup-mcp.sh
./setup-mcp.sh
```

### Step 2: Start Database
```bash
docker-compose up -d postgres
```

### Step 3: Restart VS Code
Close and reopen VS Code to load the MCP configuration.

## Test Your Setup

Open GitHub Copilot Chat in VS Code and try these commands:

1. **File Operations**: "List all TypeScript files in the backend directory"
2. **Database Access**: "Show me the structure of the users table"
3. **Git Operations**: "What are the recent commits in this repository?"
4. **Memory**: "Remember that we're using PostgreSQL for this tax application"

## Key Benefits

- **Enhanced Context**: AI has access to your files, database, and git history
- **Database Integration**: Direct SQL queries and schema inspection
- **Persistent Memory**: Context retention across chat sessions
- **File Operations**: Efficient navigation and file management
- **Version Control**: Better git integration and history access

## Troubleshooting

### MCP Not Working?
1. Ensure GitHub Copilot extension is installed and active
2. Restart VS Code after configuration changes
3. Check that Node.js 18+ is installed
4. Verify PostgreSQL is running on localhost:5432

### Database Connection Issues?
1. Start PostgreSQL: `docker-compose up postgres`
2. Check connection string in `.vscode/settings.json`
3. Verify database credentials match your Docker setup

### Permission Issues?
1. Ensure VS Code has necessary permissions
2. Check that npx is available in your PATH
3. Try running VS Code as administrator (Windows)

## Optional Enhancements

### Brave Search API
1. Get API key from: https://api.search.brave.com/
2. Update `BRAVE_API_KEY` in `.vscode/settings.json`
3. Restart VS Code

### Custom Environment
1. Copy `.env.mcp` to `.env.local`
2. Update with your specific values
3. Source the environment file

## What's Next?

1. **Explore Capabilities**: Try different MCP commands in Copilot Chat
2. **Database Queries**: Use MCP to inspect and query your tax database
3. **File Navigation**: Let AI help you navigate your codebase
4. **Testing Integration**: Use MCP for enhanced testing workflows
5. **Documentation**: Search for best practices and documentation

## Support

- **Full Guide**: See `MCP_SETUP_GUIDE.md` for detailed instructions
- **Verification**: Run `node scripts/verify-mcp-setup.js` to check setup
- **MCP Documentation**: https://modelcontextprotocol.io/
- **VS Code Tasks**: Use Ctrl+Shift+P → "Tasks: Run Task" for MCP operations

Your MCP setup is now ready! Restart VS Code and start exploring enhanced AI capabilities.
