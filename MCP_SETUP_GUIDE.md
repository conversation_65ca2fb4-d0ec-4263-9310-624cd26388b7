# MCP (Model Context Protocol) Setup Guide for VS Code

This guide will help you set up MCP servers in VS Code for enhanced development capabilities with GitHub Copilot Chat.

## Prerequisites

1. **VS Code** with the latest version
2. **GitHub Copilot** extension installed and active
3. **Node.js** (version 18 or higher)
4. **npm** or **yarn** package manager

## What is MCP?

Model Context Protocol (MCP) allows AI assistants like GitHub Copilot to connect to external data sources and tools, providing richer context and capabilities during development.

## Configured MCP Servers

The following MCP servers have been configured for your tax filing application:

### 1. Filesystem Server
- **Purpose**: Provides file system access and operations
- **Usage**: Read, write, and manage files in your project
- **Configuration**: Already set to monitor your project root directory

### 2. Git Server
- **Purpose**: Git repository operations and history
- **Usage**: Access commit history, branch information, and git operations
- **Configuration**: Monitors your current repository

### 3. PostgreSQL Server
- **Purpose**: Database operations and queries
- **Usage**: Query your tax database, inspect schemas, run SQL commands
- **Configuration**: Connected to your `bikhard_tax` database
- **Connection**: `postgresql://postgres:postgres@localhost:5432/bikhard_tax`

### 4. Memory Server
- **Purpose**: Persistent memory across chat sessions
- **Usage**: Remember context and information between conversations
- **Configuration**: Stores conversation memory locally

### 5. Brave Search Server (Optional)
- **Purpose**: Web search capabilities
- **Usage**: Search for documentation, tax regulations, and technical information
- **Configuration**: Requires Brave API key (see setup below)

### 6. Puppeteer Server (Optional)
- **Purpose**: Web automation and scraping
- **Usage**: Automate web interactions, testing, and data extraction
- **Configuration**: Headless browser automation

## Setup Instructions

### Step 1: Verify Configuration
The MCP configuration has been added to `.vscode/settings.json`. The configuration includes:
- Enabled MCP servers
- Database connection for your PostgreSQL instance
- TypeScript and ESLint integration

### Step 2: Install Required Dependencies
The MCP servers will be automatically installed via `npx` when first used. No manual installation required.

### Step 3: Start Your Database (Required for PostgreSQL Server)
Make sure your PostgreSQL database is running:

```bash
# Using Docker Compose
docker-compose up postgres

# Or start the full application
docker-compose up
```

### Step 4: Optional - Configure Brave Search API
If you want to use the Brave Search server:

1. Get a Brave Search API key from: https://api.search.brave.com/
2. Update the API key in `.vscode/settings.json`:
   ```json
   "env": {
     "BRAVE_API_KEY": "your-actual-api-key-here"
   }
   ```

### Step 5: Restart VS Code
After configuration, restart VS Code to ensure all settings are loaded.

## Using MCP Servers

### In GitHub Copilot Chat
Once configured, you can use MCP capabilities in Copilot Chat:

1. **File Operations**: "Show me the contents of the user model file"
2. **Database Queries**: "What tables exist in the bikhard_tax database?"
3. **Git Operations**: "What are the recent commits in this repository?"
4. **Memory**: "Remember that we're implementing OCR for tax documents"
5. **Search**: "Search for React testing best practices"

### Example Prompts
- "Query the users table to show me the current user structure"
- "Show me the git history for the authentication files"
- "Search for Material-UI theming documentation"
- "Remember that we're using PostgreSQL instead of SQLite"
- "List all TypeScript files in the backend/src directory"

## Troubleshooting

### Common Issues

1. **MCP servers not starting**
   - Ensure Node.js is installed and accessible
   - Check that npx is available in your PATH
   - Restart VS Code after configuration changes

2. **PostgreSQL connection issues**
   - Verify your database is running on localhost:5432
   - Check database credentials match your Docker setup
   - Ensure the database name is correct (`bikhard_tax`)

3. **Permission issues**
   - Ensure VS Code has necessary permissions
   - Check that the project directory is accessible

### Verification Steps

1. Open GitHub Copilot Chat in VS Code
2. Try a simple command: "List the files in the current directory"
3. Test database access: "Show me the database schema"
4. Verify git access: "What's the current branch?"

## Benefits for Your Tax Filing Application

With MCP servers configured, you'll have enhanced capabilities for:

1. **Database Development**: Direct SQL queries and schema inspection
2. **File Management**: Efficient navigation and file operations
3. **Version Control**: Better git integration and history access
4. **Testing**: Web automation for end-to-end testing
5. **Research**: Quick access to documentation and best practices
6. **Context Retention**: Persistent memory across development sessions

## Security Considerations

- MCP servers run locally and don't send data to external services (except Brave Search if configured)
- Database credentials are used locally only
- File system access is limited to your project directory
- All operations are performed with your user permissions

## Next Steps

1. Restart VS Code to activate the configuration
2. Start your PostgreSQL database
3. Test the MCP integration with simple Copilot Chat commands
4. Optionally configure Brave Search API for enhanced search capabilities

For more information about MCP, visit: https://modelcontextprotocol.io/
