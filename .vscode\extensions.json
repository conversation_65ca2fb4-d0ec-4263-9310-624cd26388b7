{"recommendations": ["github.copilot", "github.copilot-chat", "ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "ms-vscode.vscode-json", "ms-vscode-remote.remote-containers", "ms-vscode.vscode-docker", "ckolkman.vscode-postgres", "ms-vscode.vscode-eslint", "esbenp.prettier-vscode", "ms-vscode.vscode-jest", "ms-vscode.vscode-test-adapter-api", "ms-vscode.test-adapter-converter"], "unwantedRecommendations": []}