import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Grid,
  TextField,
  Card,
  CardContent
} from '@mui/material';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';



// W-2 validation schema
const w2Schema = z.object({
  employerName: z.string().min(1, 'Employer name is required'),
  employerEin: z.string().min(1, 'Employer EIN is required'),
  employerStreet: z.string().min(1, 'Employer street is required'),
  employerCity: z.string().min(1, 'Employer city is required'),
  employerState: z.string().min(1, 'Employer state is required'),
  employerZipCode: z.string().min(1, 'Employer ZIP code is required'),
  wages: z.string().min(1, 'Wages are required'),
  federalIncomeTaxWithheld: z.string().min(1, 'Federal income tax withheld is required'),
  socialSecurityWages: z.string().min(1, 'Social Security wages are required'),
  socialSecurityTaxWithheld: z.string().min(1, 'Social Security tax withheld is required'),
  medicareWages: z.string().min(1, 'Medicare wages are required'),
  medicareTaxWithheld: z.string().min(1, 'Medicare tax withheld is required'),
});

type W2FormData = z.infer<typeof w2Schema>;

interface EnhancedW2FormProps {
  taxYear: number;
  onSubmit?: (data: W2FormData) => void;
  initialData?: Partial<W2FormData>;
}

const EnhancedW2Form: React.FC<EnhancedW2FormProps> = ({
  taxYear,
  onSubmit,
  initialData
}) => {

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<W2FormData>({
    resolver: zodResolver(w2Schema),
    defaultValues: {
      employerName: '',
      employerEin: '',
      employerStreet: '',
      employerCity: '',
      employerState: '',
      employerZipCode: '',
      wages: '',
      federalIncomeTaxWithheld: '',
      socialSecurityWages: '',
      socialSecurityTaxWithheld: '',
      medicareWages: '',
      medicareTaxWithheld: '',
      ...initialData
    },
  });

  const renderField = (
    fieldName: keyof W2FormData,
    label: string,
    required: boolean = true,
    type: string = 'text'
  ) => {
    return (
      <Controller
        name={fieldName}
        control={control}
        render={({ field }) => (
          <TextField
            {...field}
            required={required}
            fullWidth
            label={label}
            type={type}
            InputProps={{
              inputProps: type === 'number' ? { min: 0, step: 0.01 } : {},
            }}
            error={!!errors[fieldName]}
            helperText={errors[fieldName]?.message}
          />
        )}
      />
    );
  };

  const onFormSubmit = (data: W2FormData) => {
    if (onSubmit) {
      onSubmit(data);
    }
  };

  return (
    <Box>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          W-2 Information for {taxYear}
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          Please enter your W-2 information manually. All fields are required for accurate tax calculation.
        </Typography>

        <form onSubmit={handleSubmit(onFormSubmit)}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Employer Information
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  {renderField('employerName', 'Employer Name')}
                </Grid>
                <Grid item xs={12} sm={6}>
                  {renderField('employerEin', 'Employer EIN')}
                </Grid>
                <Grid item xs={12}>
                  {renderField('employerStreet', 'Employer Street Address')}
                </Grid>
                <Grid item xs={12} sm={6}>
                  {renderField('employerCity', 'City')}
                </Grid>
                <Grid item xs={12} sm={3}>
                  {renderField('employerState', 'State')}
                </Grid>
                <Grid item xs={12} sm={3}>
                  {renderField('employerZipCode', 'ZIP Code')}
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Income and Tax Information
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  {renderField('wages', 'Wages (Box 1)', true, 'number')}
                </Grid>
                <Grid item xs={12} sm={6}>
                  {renderField('federalIncomeTaxWithheld', 'Federal Income Tax Withheld (Box 2)', true, 'number')}
                </Grid>
                <Grid item xs={12} sm={6}>
                  {renderField('socialSecurityWages', 'Social Security Wages (Box 3)', true, 'number')}
                </Grid>
                <Grid item xs={12} sm={6}>
                  {renderField('socialSecurityTaxWithheld', 'Social Security Tax Withheld (Box 4)', true, 'number')}
                </Grid>
                <Grid item xs={12} sm={6}>
                  {renderField('medicareWages', 'Medicare Wages (Box 5)', true, 'number')}
                </Grid>
                <Grid item xs={12} sm={6}>
                  {renderField('medicareTaxWithheld', 'Medicare Tax Withheld (Box 6)', true, 'number')}
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
            <Button variant="outlined" onClick={() => reset()}>
              Reset Form
            </Button>
            <Button type="submit" variant="contained">
              Save W-2 Information
            </Button>
          </Box>
        </form>
      </Paper>

    </Box>
  );
};

export default EnhancedW2Form;
