import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogT<PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  Grid,
  Paper,
  Divider,
  Alert,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
} from '@mui/material';
import { Add, Delete, Calculate } from '@mui/icons-material';

interface ExpenseItem {
  id: string;
  description: string;
  amount: number;
  date?: string;
  businessPercentage?: number;
}

interface ExpenseCalculatorProps {
  open: boolean;
  onClose: () => void;
  onSave: (total: number) => void;
  title: string;
  category: 'mileage' | 'home-office' | 'meals' | 'general';
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`calculator-tabpanel-${index}`}
      aria-labelledby={`calculator-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export const ExpenseCalculator: React.FC<ExpenseCalculatorProps> = ({
  open,
  onClose,
  onSave,
  title,
  category,
}) => {
  const [tabValue, setTabValue] = useState(0);
  const [expenses, setExpenses] = useState<ExpenseItem[]>([]);
  const [newExpense, setNewExpense] = useState<Partial<ExpenseItem>>({
    description: '',
    amount: 0,
    businessPercentage: 100,
  });

  // Mileage calculator state
  const [mileageEntries, setMileageEntries] = useState<Array<{
    id: string;
    date: string;
    description: string;
    miles: number;
  }>>([]);
  const [mileageRate] = useState(0.655); // 2023 standard mileage rate

  // Home office calculator state
  const [homeOfficeData, setHomeOfficeData] = useState({
    homeSquareFootage: 0,
    officeSquareFootage: 0,
    totalUtilities: 0,
    totalMortgageRent: 0,
    totalInsurance: 0,
    totalRepairs: 0,
  });

  const addExpense = () => {
    if (newExpense.description && newExpense.amount) {
      const expense: ExpenseItem = {
        id: Date.now().toString(),
        description: newExpense.description,
        amount: newExpense.amount,
        businessPercentage: newExpense.businessPercentage || 100,
      };
      setExpenses([...expenses, expense]);
      setNewExpense({ description: '', amount: 0, businessPercentage: 100 });
    }
  };

  const removeExpense = (id: string) => {
    setExpenses(expenses.filter(exp => exp.id !== id));
  };

  const addMileageEntry = () => {
    const entry = {
      id: Date.now().toString(),
      date: '',
      description: '',
      miles: 0,
    };
    setMileageEntries([...mileageEntries, entry]);
  };

  const updateMileageEntry = (id: string, field: string, value: string | number) => {
    setMileageEntries(entries =>
      entries.map(entry =>
        entry.id === id ? { ...entry, [field]: value } : entry
      )
    );
  };

  const removeMileageEntry = (id: string) => {
    setMileageEntries(entries => entries.filter(entry => entry.id !== id));
  };

  const calculateTotal = () => {
    switch (category) {
      case 'mileage':
        return mileageEntries.reduce((total, entry) => total + (entry.miles * mileageRate), 0);
      case 'home-office':
        const officePercentage = homeOfficeData.homeSquareFootage > 0 
          ? homeOfficeData.officeSquareFootage / homeOfficeData.homeSquareFootage 
          : 0;
        return (homeOfficeData.totalUtilities + homeOfficeData.totalMortgageRent + 
                homeOfficeData.totalInsurance + homeOfficeData.totalRepairs) * officePercentage;
      case 'meals':
        // Meals are typically 50% deductible
        return expenses.reduce((total, exp) => 
          total + (exp.amount * (exp.businessPercentage || 100) / 100 * 0.5), 0);
      default:
        return expenses.reduce((total, exp) => 
          total + (exp.amount * (exp.businessPercentage || 100) / 100), 0);
    }
  };

  const handleSave = () => {
    const total = calculateTotal();
    onSave(total);
    onClose();
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>{title} Calculator</DialogTitle>
      <DialogContent>
        {category === 'mileage' ? (
          <Box>
            <Alert severity="info" sx={{ mb: 2 }}>
              Standard mileage rate for 2023: ${mileageRate} per mile
            </Alert>
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Mileage Entries</Typography>
              <Button startIcon={<Add />} onClick={addMileageEntry}>
                Add Entry
              </Button>
            </Box>

            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Date</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell>Miles</TableCell>
                    <TableCell>Amount</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {mileageEntries.map((entry) => (
                    <TableRow key={entry.id}>
                      <TableCell>
                        <TextField
                          type="date"
                          size="small"
                          value={entry.date}
                          onChange={(e) => updateMileageEntry(entry.id, 'date', e.target.value)}
                        />
                      </TableCell>
                      <TableCell>
                        <TextField
                          size="small"
                          placeholder="Business purpose"
                          value={entry.description}
                          onChange={(e) => updateMileageEntry(entry.id, 'description', e.target.value)}
                        />
                      </TableCell>
                      <TableCell>
                        <TextField
                          type="number"
                          size="small"
                          value={entry.miles}
                          onChange={(e) => updateMileageEntry(entry.id, 'miles', parseFloat(e.target.value) || 0)}
                        />
                      </TableCell>
                      <TableCell>
                        ${(entry.miles * mileageRate).toFixed(2)}
                      </TableCell>
                      <TableCell>
                        <IconButton size="small" onClick={() => removeMileageEntry(entry.id)}>
                          <Delete />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        ) : category === 'home-office' ? (
          <Box>
            <Alert severity="info" sx={{ mb: 2 }}>
              Calculate the business percentage of your home expenses based on office space.
            </Alert>
            
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Total Home Square Footage"
                  type="number"
                  value={homeOfficeData.homeSquareFootage}
                  onChange={(e) => setHomeOfficeData({
                    ...homeOfficeData,
                    homeSquareFootage: parseFloat(e.target.value) || 0
                  })}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Office Square Footage"
                  type="number"
                  value={homeOfficeData.officeSquareFootage}
                  onChange={(e) => setHomeOfficeData({
                    ...homeOfficeData,
                    officeSquareFootage: parseFloat(e.target.value) || 0
                  })}
                />
              </Grid>
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary">
                  Business percentage: {homeOfficeData.homeSquareFootage > 0 
                    ? ((homeOfficeData.officeSquareFootage / homeOfficeData.homeSquareFootage) * 100).toFixed(1)
                    : 0}%
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Annual Utilities"
                  type="number"
                  value={homeOfficeData.totalUtilities}
                  onChange={(e) => setHomeOfficeData({
                    ...homeOfficeData,
                    totalUtilities: parseFloat(e.target.value) || 0
                  })}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Annual Mortgage/Rent"
                  type="number"
                  value={homeOfficeData.totalMortgageRent}
                  onChange={(e) => setHomeOfficeData({
                    ...homeOfficeData,
                    totalMortgageRent: parseFloat(e.target.value) || 0
                  })}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Annual Insurance"
                  type="number"
                  value={homeOfficeData.totalInsurance}
                  onChange={(e) => setHomeOfficeData({
                    ...homeOfficeData,
                    totalInsurance: parseFloat(e.target.value) || 0
                  })}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Annual Repairs"
                  type="number"
                  value={homeOfficeData.totalRepairs}
                  onChange={(e) => setHomeOfficeData({
                    ...homeOfficeData,
                    totalRepairs: parseFloat(e.target.value) || 0
                  })}
                />
              </Grid>
            </Grid>
          </Box>
        ) : (
          <Box>
            {category === 'meals' && (
              <Alert severity="warning" sx={{ mb: 2 }}>
                Business meals are generally 50% deductible. This calculator applies that limitation.
              </Alert>
            )}
            
            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Description"
                  value={newExpense.description}
                  onChange={(e) => setNewExpense({ ...newExpense, description: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="Amount"
                  type="number"
                  value={newExpense.amount}
                  onChange={(e) => setNewExpense({ ...newExpense, amount: parseFloat(e.target.value) || 0 })}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="Business %"
                  type="number"
                  value={newExpense.businessPercentage}
                  onChange={(e) => setNewExpense({ ...newExpense, businessPercentage: parseFloat(e.target.value) || 100 })}
                  inputProps={{ min: 0, max: 100 }}
                />
              </Grid>
              <Grid item xs={12} md={2}>
                <Button fullWidth variant="contained" onClick={addExpense} startIcon={<Add />}>
                  Add
                </Button>
              </Grid>
            </Grid>

            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Description</TableCell>
                    <TableCell>Amount</TableCell>
                    <TableCell>Business %</TableCell>
                    <TableCell>Deductible</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {expenses.map((expense) => {
                    const deductibleAmount = expense.amount * (expense.businessPercentage || 100) / 100;
                    const finalAmount = category === 'meals' ? deductibleAmount * 0.5 : deductibleAmount;
                    
                    return (
                      <TableRow key={expense.id}>
                        <TableCell>{expense.description}</TableCell>
                        <TableCell>${expense.amount.toFixed(2)}</TableCell>
                        <TableCell>{expense.businessPercentage}%</TableCell>
                        <TableCell>${finalAmount.toFixed(2)}</TableCell>
                        <TableCell>
                          <IconButton size="small" onClick={() => removeExpense(expense.id)}>
                            <Delete />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        )}

        <Divider sx={{ my: 2 }} />
        
        <Paper sx={{ p: 2, bgcolor: 'primary.light', color: 'primary.contrastText' }}>
          <Typography variant="h6" align="center">
            Total Deductible Amount: ${calculateTotal().toFixed(2)}
          </Typography>
        </Paper>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSave} variant="contained" startIcon={<Calculate />}>
          Use This Amount
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ExpenseCalculator;
