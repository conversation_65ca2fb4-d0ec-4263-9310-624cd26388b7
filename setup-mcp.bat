@echo off
echo Setting up MCP (Model Context Protocol) for VS Code...
echo.

echo 1. Checking Node.js installation...
node --version
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo.
echo 2. Checking npx availability...
npx --version
if %errorlevel% neq 0 (
    echo Error: npx is not available
    pause
    exit /b 1
)

echo.
echo 3. Starting PostgreSQL database...
docker-compose up -d postgres
if %errorlevel% neq 0 (
    echo Warning: Could not start PostgreSQL with Docker
    echo Make sure Docker is running and try: docker-compose up postgres
)

echo.
echo 4. Testing MCP server accessibility...
echo Testing filesystem server...
npx -y @modelcontextprotocol/server-filesystem --help >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Filesystem server accessible
) else (
    echo ✗ Filesystem server needs installation
)

echo Testing git server...
npx -y @modelcontextprotocol/server-git --help >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Git server accessible
) else (
    echo ✗ Git server needs installation
)

echo Testing postgres server...
npx -y @modelcontextprotocol/server-postgres --help >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ PostgreSQL server accessible
) else (
    echo ✗ PostgreSQL server needs installation
)

echo.
echo 5. Running verification script...
node scripts/verify-mcp-setup.js

echo.
echo MCP Setup Complete!
echo.
echo Next steps:
echo 1. Restart VS Code to load the new configuration
echo 2. Open GitHub Copilot Chat
echo 3. Test with: "List files in current directory"
echo 4. Test database: "Show me the database schema"
echo.
echo For detailed instructions, see: MCP_SETUP_GUIDE.md
echo.
pause
