{"github.copilot.chat.experimental.mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "."]}, "git": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-git", "--repository", "."]}, "postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://postgres:postgres@localhost:5432/bikhard_tax"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "your-brave-api-key-here"}}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}}, "github.copilot.chat.experimental.mcpEnabled": true, "typescript.preferences.includePackageJsonAutoImports": "auto", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "files.associations": {"*.tsx": "typescriptreact", "*.ts": "typescript"}}