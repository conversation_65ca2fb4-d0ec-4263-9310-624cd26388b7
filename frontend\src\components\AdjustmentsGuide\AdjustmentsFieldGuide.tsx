import React, { useState } from 'react';
import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Paper,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Card,
  CardContent,
} from '@mui/material';
import {
  ExpandMore,
  Info,
  Warning,
  CheckCircle,
  Error as ErrorIcon,
  Help,
  Calculate,
  School,
  MedicalServices,
  Savings,
  Business,
  Home,
} from '@mui/icons-material';

interface AdjustmentGuideProps {
  adjustmentType: string;
  value?: string | number;
  onCalculatorOpen?: () => void;
}

interface AdjustmentGuideContent {
  title: string;
  description: string;
  eligibility: string[];
  limits: string[];
  examples: string[];
  tips: string[];
  warnings?: string[];
  required_forms?: string[];
  irs_reference?: string;
  common_mistakes?: string[];
  phase_out_limits?: string;
}

const adjustmentGuides: Record<string, AdjustmentGuideContent> = {
  educatorExpenses: {
    title: 'Educator Expenses',
    description: 'Qualified expenses paid by eligible educators for classroom supplies and materials.',
    eligibility: [
      'Must be a K-12 teacher, instructor, counselor, principal, or aide',
      'Must work at least 900 hours during the school year',
      'Must work at a school that provides elementary or secondary education'
    ],
    limits: ['Maximum deduction: $300 for 2023'],
    examples: [
      'Books and supplies used in the classroom',
      'Computer equipment and software for educational use',
      'Athletic supplies for physical education classes',
      'Generally accepted professional development courses'
    ],
    tips: [
      'Keep all receipts for qualified expenses',
      'Expenses must be ordinary and necessary',
      'Cannot be reimbursed by your school or other source',
      'Deduction is taken above-the-line (reduces AGI)'
    ],
    warnings: [
      'Personal use items do not qualify',
      'Reimbursed expenses cannot be deducted'
    ],
    irs_reference: 'Publication 529, Form 1040 instructions',
    title: 'Educator Expenses'
  },
  studentLoanInterest: {
    title: 'Student Loan Interest',
    description: 'Interest paid on qualified student loans for higher education expenses.',
    eligibility: [
      'Must have paid interest on a qualified student loan',
      'Loan must have been used for qualified education expenses',
      'Cannot be married filing separately if spouse also paid student loan interest',
      'Income limits apply (phase-out begins at $75,000 for single filers)'
    ],
    limits: ['Maximum deduction: $2,500 per year'],
    examples: [
      'Interest on federal student loans',
      'Interest on private student loans for qualified education',
      'Interest on refinanced student loans'
    ],
    tips: [
      'You should receive Form 1098-E if you paid $600+ in interest',
      'Deduction phases out at higher income levels',
      'Can deduct even if you don\'t itemize',
      'Voluntary interest payments also qualify'
    ],
    warnings: [
      'Cannot deduct if someone else can claim you as a dependent',
      'Income limits may reduce or eliminate the deduction'
    ],
    phase_out_limits: 'Phases out between $75,000-$90,000 (single) or $155,000-$185,000 (married filing jointly)',
    required_forms: ['Form 1098-E (if available)'],
    irs_reference: 'Publication 970'
  },
  iraDeduction: {
    title: 'IRA Deduction',
    description: 'Contributions to traditional Individual Retirement Accounts (IRAs).',
    eligibility: [
      'Must have earned income',
      'Must be under age 70½ (for tax years before 2020)',
      'Deduction may be limited if covered by workplace retirement plan'
    ],
    limits: [
      '2023 contribution limit: $6,500 ($7,500 if age 50+)',
      'Deduction phases out at higher income levels if covered by workplace plan'
    ],
    examples: [
      '$6,500 contribution to traditional IRA',
      '$7,500 contribution if age 50 or older',
      'Spousal IRA contribution for non-working spouse'
    ],
    tips: [
      'Contributions can be made until tax filing deadline',
      'Consider Roth vs. traditional IRA based on current vs. future tax rates',
      'Spousal IRAs allow contributions for non-working spouses',
      'Required minimum distributions begin at age 73'
    ],
    warnings: [
      'Early withdrawal penalties may apply before age 59½',
      'Deduction limits apply if covered by workplace retirement plan'
    ],
    phase_out_limits: 'Deduction phases out between $73,000-$83,000 (single) or $116,000-$136,000 (married filing jointly) if covered by workplace plan',
    irs_reference: 'Publication 590-A'
  },
  selfEmploymentTax: {
    title: 'Self-Employment Tax Deduction',
    description: 'Deduction for the employer-equivalent portion of self-employment tax.',
    eligibility: [
      'Must have paid self-employment tax',
      'Must have net earnings from self-employment of $400 or more'
    ],
    limits: ['Deduction is 50% of self-employment tax paid'],
    examples: [
      'Freelancer with $50,000 net self-employment income',
      'Consultant with multiple clients',
      'Small business owner operating as sole proprietorship'
    ],
    tips: [
      'Automatically calculated if you file Schedule SE',
      'Reduces both income tax and self-employment tax',
      'Taken above-the-line (reduces AGI)',
      'No additional forms needed beyond Schedule SE'
    ],
    warnings: [
      'Must actually pay self-employment tax to claim deduction',
      'Cannot exceed 50% of self-employment tax paid'
    ],
    required_forms: ['Schedule SE'],
    irs_reference: 'Publication 334, Schedule SE instructions'
  },
  healthSavingsAccount: {
    title: 'Health Savings Account (HSA)',
    description: 'Contributions to Health Savings Accounts for qualified medical expenses.',
    eligibility: [
      'Must be covered by a High Deductible Health Plan (HDHP)',
      'Cannot be enrolled in Medicare',
      'Cannot be claimed as a dependent on someone else\'s return',
      'Cannot have other health coverage (with some exceptions)'
    ],
    limits: [
      '2023 contribution limits: $3,850 (self-only) or $7,750 (family)',
      'Additional $1,000 catch-up contribution if age 55+'
    ],
    examples: [
      '$3,850 contribution for individual HDHP coverage',
      '$7,750 contribution for family HDHP coverage',
      '$4,850 contribution for individual age 55+ ($3,850 + $1,000 catch-up)'
    ],
    tips: [
      'Triple tax advantage: deductible, tax-free growth, tax-free withdrawals for qualified expenses',
      'Contributions can be made until tax filing deadline',
      'Funds roll over year to year',
      'Can be used for retirement after age 65'
    ],
    warnings: [
      '20% penalty for non-qualified withdrawals before age 65',
      'Must maintain HDHP coverage to contribute',
      'Contribution limits are prorated if not covered for full year'
    ],
    required_forms: ['Form 8889'],
    irs_reference: 'Publication 969'
  },
  movingExpenses: {
    title: 'Moving Expenses',
    description: 'Qualified moving expenses for job-related moves (limited to military members for 2018-2025).',
    eligibility: [
      'For 2018-2025: Only active duty military members moving due to military orders',
      'Move must be closely related to start of work',
      'Must meet distance and time tests (for military)'
    ],
    limits: ['No dollar limit, but expenses must be reasonable'],
    examples: [
      'Transportation of household goods and personal effects',
      'Travel expenses to new home',
      'Storage costs for up to 30 days'
    ],
    tips: [
      'Keep detailed records of all moving expenses',
      'Reimbursed expenses cannot be deducted',
      'Some military allowances may not be taxable'
    ],
    warnings: [
      'For 2018-2025, only military members can deduct moving expenses',
      'Deduction suspended for non-military taxpayers until 2026'
    ],
    required_forms: ['Form 3903 (for military members)'],
    irs_reference: 'Publication 521'
  }
};

export const AdjustmentsFieldGuide: React.FC<AdjustmentGuideProps> = ({
  adjustmentType,
  value,
  onCalculatorOpen
}) => {
  const [detailsOpen, setDetailsOpen] = useState(false);
  const guide = adjustmentGuides[adjustmentType];

  if (!guide) {
    return null;
  }

  const getIcon = () => {
    switch (adjustmentType) {
      case 'educatorExpenses':
        return <School color="primary" />;
      case 'studentLoanInterest':
        return <School color="info" />;
      case 'iraDeduction':
        return <Savings color="success" />;
      case 'selfEmploymentTax':
        return <Business color="warning" />;
      case 'healthSavingsAccount':
        return <MedicalServices color="error" />;
      case 'movingExpenses':
        return <Home color="secondary" />;
      default:
        return <Info />;
    }
  };

  return (
    <Box>
      <Button
        size="small"
        startIcon={<Help />}
        onClick={() => setDetailsOpen(true)}
        sx={{ mb: 1 }}
      >
        Adjustment Guide
      </Button>

      <Dialog
        open={detailsOpen}
        onClose={() => setDetailsOpen(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {getIcon()}
            <Typography variant="h6">{guide.title}</Typography>
            <Chip label="Above-the-Line Deduction" color="success" size="small" />
          </Box>
        </DialogTitle>

        <DialogContent>
          <Typography variant="body1" paragraph>
            {guide.description}
          </Typography>

          <Grid container spacing={3}>
            {/* Eligibility Requirements */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary">
                    Eligibility Requirements
                  </Typography>
                  <List dense>
                    {guide.eligibility.map((requirement, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <CheckCircle color="success" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText primary={requirement} />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            </Grid>

            {/* Limits and Restrictions */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="warning.main">
                    Limits & Restrictions
                  </Typography>
                  <List dense>
                    {guide.limits.map((limit, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <Warning color="warning" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText primary={limit} />
                      </ListItem>
                    ))}
                  </List>
                  {guide.phase_out_limits && (
                    <Alert severity="warning" sx={{ mt: 1 }}>
                      <Typography variant="body2">
                        <strong>Income Phase-out:</strong> {guide.phase_out_limits}
                      </Typography>
                    </Alert>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Examples */}
          <Accordion sx={{ mt: 2 }}>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography variant="subtitle1">Examples</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                {guide.examples.map((example, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <CheckCircle color="success" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={example} />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>

          {/* Tips */}
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography variant="subtitle1">Tips & Best Practices</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                {guide.tips.map((tip, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <Info color="info" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={tip} />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>

          {/* Warnings */}
          {guide.warnings && guide.warnings.length > 0 && (
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Typography variant="subtitle1" color="warning.main">
                  Important Warnings
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <List dense>
                  {guide.warnings.map((warning, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <Warning color="warning" fontSize="small" />
                      </ListItemIcon>
                      <ListItemText primary={warning} />
                    </ListItem>
                  ))}
                </List>
              </AccordionDetails>
            </Accordion>
          )}

          {/* Common Mistakes */}
          {guide.common_mistakes && guide.common_mistakes.length > 0 && (
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Typography variant="subtitle1" color="error.main">
                  Common Mistakes to Avoid
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <List dense>
                  {guide.common_mistakes.map((mistake, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <ErrorIcon color="error" fontSize="small" />
                      </ListItemIcon>
                      <ListItemText primary={mistake} />
                    </ListItem>
                  ))}
                </List>
              </AccordionDetails>
            </Accordion>
          )}

          {/* Required Forms */}
          {guide.required_forms && guide.required_forms.length > 0 && (
            <Paper sx={{ p: 2, mt: 2, bgcolor: 'info.light' }}>
              <Typography variant="subtitle2" gutterBottom>
                Required Forms
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {guide.required_forms.map((form, index) => (
                  <Chip key={index} label={form} color="info" size="small" />
                ))}
              </Box>
            </Paper>
          )}

          {/* IRS Reference */}
          {guide.irs_reference && (
            <Paper sx={{ p: 2, mt: 2, bgcolor: 'grey.50' }}>
              <Typography variant="subtitle2" gutterBottom>
                IRS Reference
              </Typography>
              <Typography variant="body2">
                {guide.irs_reference}
              </Typography>
            </Paper>
          )}

          {/* Current Value */}
          {value !== undefined && (
            <Alert severity="info" sx={{ mt: 2 }}>
              <Typography variant="body2">
                Current value: {typeof value === 'number' ? `$${value.toLocaleString()}` : value}
              </Typography>
            </Alert>
          )}
        </DialogContent>

        <DialogActions>
          {onCalculatorOpen && (
            <Button onClick={onCalculatorOpen} startIcon={<Calculate />}>
              Open Calculator
            </Button>
          )}
          <Button onClick={() => setDetailsOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdjustmentsFieldGuide;
