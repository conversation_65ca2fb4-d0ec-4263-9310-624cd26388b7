# Test Environment Configuration
NODE_ENV=test

# Server configuration
PORT=5001

# Database Configuration (PostgreSQL for testing - consistent with production)
DB_DIALECT=postgres
DB_HOST=localhost
DB_PORT=5433
DB_NAME=bikhard_tax_test
DB_USER=postgres
DB_PASSWORD=postgres
DB_LOGGING=false

# JWT Configuration
JWT_SECRET=test_jwt_secret_key_for_testing_only
JWT_EXPIRES_IN=1d

# File Upload Configuration
UPLOAD_DIR=./test-uploads
MAX_FILE_SIZE=10485760

# OCR Service Configuration
OCR_SERVICE=mock
OCR_API_KEY=test_key

# Frontend URL for CORS
FRONTEND_URL=http://localhost:3001

# Test-specific settings
TEST_TIMEOUT=30000
